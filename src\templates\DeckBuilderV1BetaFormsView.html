<form id="frm_crear_mazo" class="frm frm--primary">
    <h3 class="cs-color-LightGrey">¡Creador de Mazos! V1 Beta</h3>

    <div class="alert alert--info">
        <p class="alert__message">Esta herramienta se encuentra en fase v1-beta y puede no ser precisa. Se recomienda
            revisar y ajustar los mazos generados manualmente.</p>
    </div>
    <div class="alert alert--info">
        <p class="alert__message">Prueba funciones nuevas antes de que se lancen y déjanos tu feedback.</p>
    </div>

    <h3 class="cs-color-LightGrey">Opciones</h3>

    <input type="hidden" name="version" value="${ version }">
    <input type="hidden" name="algorithm" value="${ level }">

    <!-- Opciones Basicas -->

   ${ ['simple', 'balanced', 'optimized'].includes(level) ? `
    <fieldset>
        <legend>Opciones Básicas</legend>
        <div id="div_che_win">
            <input id="inp_che_win" type="checkbox" class="cs-color-DeepBlue">
            <label for="inp_che_win">Elegir una Win</label>&nbsp;<img class="cs-tooltip-image"
                src="./static/media/styles/icons/info-circle.svg" alt="inf"
                data-inf="Crea el Mazo basado en la Win Condition que elijas en el primer slot."><br><br>
            <input id="inp_win_name" type="hidden" name="params[winConditionName]" value="null">
        </div>
    ` : '' }

    <!-- Opciones Intermedias -->

    ${ ['balanced', 'optimized'].includes(level) ? `
        <div id="div_sel_prelix">
            <label for="sel_prelix">Promedio de Elixir:</label>
            <select name="params[averageElixir]" id="sel_prelix">
                <option value="null" selected>Libre</option>
                <option value="2.5">2.5</option>
                <option value="3.0">3.0</option>
                <option value="3.5">3.5</option>
                <option value="4.0">4.0</option>
                <option value="4.5">4.5</option>
                <option value="5.0">5.0</option>
                <option value="5.5">5.5</option>
            </select><br><br>
        </div>
    ` : '' }

    <!-- Opciones Avanzadas -->

    ${ level == 'optimized' ? `
        <div id="div_sel_evo">
            <label for="sel_evo">Evoluciones:</label>
            <select name="params[evolutions]" id="sel_evo">
                <option value="0" selected>Sin Evoluciones</option>
                <option value="1" disabled>1 Evo (Libre)</option>
                <option value="2">2 Evo (Libres)</option>
            </select><br><br>
        </div>
     ` : '' }
    </fieldset>

    <button class="cs-btn cs-btn--medium cs-btn--primary" type="submit">Crear</button>&nbsp;&nbsp;&nbsp;
    <button id="btn_can_cre_maz_pers" class="cs-btn cs-btn--medium cs-btn--cancel" type="button">Cancelar</button>
</form>

<!-- v1.1 -->
<div id="div_frm_crear_mazo_v1_1" style="display: none;">
    <form id="frm_crear_mazo_1_1" class="frm frm--primary" style="display: none;">
        <h3 class="cs-color-LightGrey">¡Creador de Mazos! v1.1</h3>

        <input type="hidden" name="version" value="1.1">

        <label>Versiones: </label>
        <select name="MazOpt">
            <option value="1" selected>1 Mazo</option>
            <option value="2">2 Mazos +3Gem</option>
            <option value="3">3 Mazos +5Gem</option>
        </select><br><br>

        <label>Estrategia<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="inf"
                data-inf="Son un Conjunto de Condiciones para Filtrar cartas y lograr Crear un Mazo Estrategico, elige las Estrategias para Crear el Mazo Basada en Ella.">:</label>&nbsp;<button
            type="button" class="btn_crear_est">Crear</button>&nbsp;<button type="button" class="btn_eleccion_est"
            data-type="crear">Elegir..</button><br>
        <input type="hidden" name="idsEstrategias" value="[]">
        <div id="div_est_cre" class="div_estrategias_seleccionadas">
            <p>Estrategias Seleccionadas:</p>
        </div>
        <button class="cs-btn cs-btn--medium cs-btn--primary" type="submit">Crear</button>&nbsp;&nbsp;&nbsp;
        <button id="btn_concelar_crear_mazo_v1_1" class="cs-btn cs-btn--medium cs-btn--cancel"
            type="button">Cancelar</button>
    </form>
</div>