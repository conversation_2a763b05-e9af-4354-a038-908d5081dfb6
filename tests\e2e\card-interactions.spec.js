/**
 * @file End-to-end tests for card interactions on the Clash Strategic web application.
 * This suite verifies the functionality of interacting with cards, including viewing stats,
 * adding cards to a deck, and removing them.
 * @group Card Interactions
 */
import { test, expect } from "@playwright/test";

/**
 * @description Test suite for verifying card interaction functionality.
 */
test.describe("Clash Strategic - Card Interactions", () => {
  let consoleMessages;

  /**
   * @description Navigates to the home page and waits for the cards to be loaded before each test.
   */
  test.beforeEach(async ({ page }) => {
    // Reset and start collecting console messages before each test
    consoleMessages = [];
    page.on("console", (msg) => {
      consoleMessages.push(msg);
    });

    await page.goto("/home.html");
    await page.waitForResponse(
      (response) =>
        response.url().includes("cards.json") && response.status() === 200,
      { timeout: 15000 }
    );
    await expect(page.locator("#div_cards_all")).toBeVisible({
      timeout: 15000,
    });

    // Close the welcome modal if it appears
    const welcomeModal = page.locator("#div_tog_gen");
    if (await welcomeModal.isVisible()) {
      await welcomeModal.locator(".btn_x_perfil").click();
      await expect(welcomeModal).toBeHidden();
    }
  });

  /**
   * @description Checks for console errors after each test, allowing warnings.
   */
  test.afterEach(async () => {
    // Filter for errors, allowing warnings
    const errors = consoleMessages.filter((msg) => msg.type() === "error");
    const warnings = consoleMessages.filter((msg) => msg.type() === "warn");

    // Display warnings in the test output without failing the test
    if (warnings.length > 0) {
      console.warn(`\nFound ${warnings.length} console warnings:`);
      warnings.forEach((w) => {
        const location = w.location();
        console.warn(
          `- ${w.text()} (at ${location.url}:${location.lineNumber}:${location.columnNumber
          })`
        );
      });
    }

    // Assert that no errors were logged to the console
    if (errors.length > 0) {
      const errorDetails = errors
        .map((e) => {
          const location = e.location();
          return `${e.text()} (at ${location.url}:${location.lineNumber
            }:${location.columnNumber})`;
        })
        .join("\n- ");
      const errorMessage = `Test failed due to ${errors.length
        } console errors:\n- ${errorDetails}`;
      expect(errors, errorMessage).toHaveLength(0);
    }
  });

  // Test interactions for both 'card' and 'tower' types
  const cardTypes = ["card", "tower"];

  for (const cardType of cardTypes) {
    test.describe(`Interactions for ${cardType}s`, () => {
      /**
       * @description Test to verify that clicking the "Info" button on a card shows its stats.
       */
      test(`should show ${cardType} stats when "Info" button is clicked`, async ({ page }) => {
        // Find the first card of the current type and click it to show options
        const firstCard = page.locator(`.cs-card[data-type="${cardType}"]`).first();
        await firstCard.click();

        // Click the "Info" button
        const infoButton = firstCard.locator("button.cs-card__info");
        await infoButton.click();

        // Verify that the stats modal is visible
        const statsModal = page.locator("#div_card_info");
        await expect(statsModal).toBeVisible();

        // Verify that the modal contains stats
        const statsContent = statsModal.locator(".cs-table");
        await expect(statsContent).toBeAttached();
      });

      if (cardType === 'card') {
        /**
         * @description Test to verify that clicking the "Usar" button adds the card to the deck.
         */
        test('should add card to deck when "Usar" button is clicked', async ({ page }) => {
          // Find the first card that is not in the deck
          const firstCard = page.locator('.cs-card[data-type="card"][data-inmazo="no"]').first();
          const cardName = await firstCard.getAttribute("data-name");

          // Click the card to show options
          await firstCard.click();

          // Click the "Usar" button
          const useButton = firstCard.locator("button.cs-card__use-remove");
          await useButton.click();

          // Verify that the card is now in the deck
          const deckSlot = page.locator(
            `#deck-slots-main .cs-card[data-name="${cardName}"]`
          );
          await expect(deckSlot).toBeVisible();
        });

        /**
         * @description Test to verify that clicking the "Eliminar" button removes the card from the deck.
         */
        test('should remove card from deck when "Eliminar" button is clicked', async ({ page }) => {
          // First, add a card to the deck
          const firstCard = page.locator('.cs-card[data-type="card"][data-inmazo="no"]').first();
          const cardName = await firstCard.getAttribute("data-name");
          await firstCard.click();
          await firstCard.locator("button.cs-card__use-remove").click();

          // Now, find the card in the deck and click it
          const cardInDeck = page.locator(
            `#deck-slots-main .cs-card[data-name="${cardName}"]`
          );
          await cardInDeck.click();

          // Click the "Eliminar" button
          const removeButton = cardInDeck.locator("button.cs-card__use-remove");
          await removeButton.click();

          // Verify that the card is no longer in the deck
          await expect(cardInDeck).not.toBeVisible();
        });
      }
    });
  }
});