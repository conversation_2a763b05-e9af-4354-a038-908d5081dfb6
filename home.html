<!DOCTYPE html>
<html lang="es">
  <head>
    <title>Clash Strategic</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- <link rel="manifest" href="./App/Data/manifest.json">
  <link rel="manifest" href="./App/Data/manifest.webmanifest"> -->
    <link
      rel="icon"
      type="image/png"
      sizes="512x512"
      href="./static/media/styles/logo/logo_cs.webp"
    />

    <!-- ESTILOS CSS -->
    <link rel="stylesheet" type="text/css" href="./src/css/main.css" />

    <!-- Li<PERSON>rias -->
    <script src="https://app.lemonsqueezy.com/js/lemon.js"></script>
    <script src="./src/js/libraries/js/jquery.min.js"></script>
    <script src="./src/js/libraries/js/htmx.min.js"></script>
    <script src="./src/js/libraries/js/jquery.inview.js"></script>
    <script src="./src/js/libraries/image/pica.min.js"></script>
    <script src="./src/js/libraries/ui/slick-1.8.1/slick/slick.min.js"></script>
    <script src="./src/js/libraries/ui/chart/chart.umd.js"></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="./src/js/libraries/ui/slick-1.8.1/slick/slick.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="./src/js/libraries/ui/slick-1.8.1/slick/slick-theme.css"
    />

    <!-- Java Script -->
    <script type="module" src="./src/js/main.js" defer></script>
    <script src="installsw.js"></script>
  </head>

  <body>
    <div class="line_diamond"></div>

    <div id="capa_contenido" class="cs-layer cs-layer--default"></div>

    <div id="cargando" class="cs-layer cs-layer--light">
      <img
        class="cs-layer__icon cs-layer__icon--medium"
        src="./static/media/styles/icons/menu/logo_cargando.gif"
        alt="Cargando..."
      />
    </div>

    <audio
      id="audio_button"
      src="./static/media/styles/audio/clickboton.mp3"
      style="display: none"
    ></audio>
    <audio
      id="audio_tap_card"
      src="./static/media/styles/audio/Tap card.mp3"
      style="display: none"
    ></audio>

    <div id="div_modal" style="display: none">
      <div id="conten_modal"></div>
    </div>
  </body>
  <script>
    installSW(() => {
      function initialWebApp() {
        console.log("initialWebApp()");
        Config.setConfig();
        Config.applyConfig();

        if (localStorage.getItem("session")) {
          console.log("Session encontrada, cargando usuario...");
          api("GET", "/v1/users", "get-user");
        } else {
          console.log("No se encontró session, creando una nueva...");
          api("GET", "/v1/session", "get-session");
        }
      }

      if (window.mainJsFullyLoaded) {
        initialWebApp();
      } else {
        window.addEventListener("mainjsloaded", initialWebApp, {
          once: true,
        });
      }
    });
  </script>
</html>
