/**
 * @file End-to-end tests for the initial page load of the Clash Strategic web application.
 * This suite verifies that the index page loads correctly with its static content,
 * handles redirection to the home page, and that the home page itself loads
 * with all expected essential elements.
 * @group Initial Load
 */
import { test, expect } from "@playwright/test";

/**
 * @description Test suite for verifying the initial page load functionality.
 */
test.describe("Clash Strategic - Initial Page Load", () => {
  let consoleMessages;

  test.beforeEach(async ({ page }) => {
    // Reset and start collecting console messages before each test
    consoleMessages = [];
    page.on("console", (msg) => {
      consoleMessages.push(msg);
    });
  });

  /**
   * @description Checks for console errors after each test, allowing warnings.
   */
  test.afterEach(async () => {
    // Filter for errors, allowing warnings
    const errors = consoleMessages.filter((msg) => msg.type() === "error");
    const warnings = consoleMessages.filter((msg) => msg.type() === "warn");

    // Display warnings in the test output without failing the test
    if (warnings.length > 0) {
      console.warn(`\nFound ${warnings.length} console warnings:`);
      warnings.forEach((w) => {
        const location = w.location();
        console.warn(
          `- ${w.text()} (at ${location.url}:${location.lineNumber}:${location.columnNumber
          })`
        );
      });
    }

    // Assert that no errors were logged to the console
    if (errors.length > 0) {
      const errorDetails = errors
        .map((e) => {
          const location = e.location();
          return `${e.text()} (at ${location.url}:${location.lineNumber
            }:${location.columnNumber})`;
        })
        .join("\n- ");
      const errorMessage = `Test failed due to ${errors.length
        } console errors:\n- ${errorDetails}`;
      expect(errors, errorMessage).toHaveLength(0);
    }
  });

  /**
   * @description Tests for the static content on the initial index page (loading screen).
   * These tests ensure that essential branding and loading indicators are present
   * before the application redirects to the main home page.
   * @group Index Page
   */
  test.describe("Index Page (Static Content)", () => {
    /**
     * @description Aborts the service worker installation and navigates to the root URL before each test.
     */
    test.beforeEach(async ({ page }) => {
      // Fulfill the request to prevent a console error, but with an empty body
      // to prevent the service worker from actually installing.
      await page.route("**/installsw.js", (route) =>
        route.fulfill({ status: 200, body: "" })
      );
      await page.goto("/");
    });

    /**
     * @description Verifies that the index page has the correct title.
     */
    test("should display the correct title", async ({ page }) => {
      await expect(page).toHaveTitle(/Crea, Analiza y Comparte.*Clash Strategic/);
    });

    /**
     * @description Ensures the logo is visible and has the correct alt text.
     */
    test("should have a visible logo with correct alt text", async ({ page }) => {
      const logo = page.locator("#img_logo_index");
      await expect(logo).toBeVisible();
      await expect(logo).toHaveAttribute("alt", "Clash Strategic");
    });

    /**
     * @description Checks that all elements of the loading bar are visible and attached to the DOM.
     */
    test("should show loading bar elements", async ({ page }) => {
      await expect(page.locator("#div_lin_loading")).toBeVisible();
      await expect(page.locator("#span_msg_load")).toBeAttached();
      await expect(page.locator("#span_num_por")).toBeAttached();
      await expect(page.locator("#div_porcentage")).toBeAttached();
    });
  });

  /**
   * @description Test suite for the redirection from the index page to the home page.
   * @group Redirection
   */
  test.describe("Index Page (Redirection)", () => {
    /**
     * @description Verifies that the index page successfully redirects to the home page.
     */
    test("should redirect to home page", async ({ page }) => {
      await page.goto("/");
      await page.waitForURL("**/home", { timeout: 15000 });
      await expect(page).toHaveTitle("Clash Strategic");
    });
  });

  /**
   * @description Test suite for verifying the content of the home page after initial load.
   * @group Home Page
   */
  test.describe("Home Page", () => {
    /**
     * @description Navigates to the home page before each test in this suite.
     */
    test.beforeEach(async ({ page }) => {
      await page.goto("/home.html");
      await page.waitForLoadState("domcontentloaded");
    });

    /**
     * @description Verifies that the home page has the correct title.
     */
    test("should have the correct title", async ({ page }) => {
      await expect(page).toHaveTitle("Clash Strategic");
    });

    /**
     * @description Checks for the presence of the main content area.
     */
    test("should have the main content area", async ({ page }) => {
      const mainContent = page.locator("#capa_contenido");
      await expect(mainContent).toBeAttached();
    });

    /**
     * @description Ensures the loading indicator element is present in the DOM.
     */
    test("should have the loading indicator", async ({ page }) => {
      const loadingIndicator = page.locator("#cargando");
      await expect(loadingIndicator).toBeAttached();
    });

    /**
     * @description Verifies that the audio elements for UI sounds are attached to the DOM.
     */
    test("should have audio elements ready", async ({ page }) => {
      await expect(page.locator("#audio_button")).toBeAttached();
      await expect(page.locator("#audio_tap_card")).toBeAttached();
    });

    /**
     * @description Confirms that the modal container is present but hidden by default.
     */
    test("should have a hidden modal container", async ({ page }) => {
      const modal = page.locator("#div_modal");
      await expect(modal).toBeHidden();
    });

    /**
     * @description Verifies that the cards section is visible and contains cards.
     */
    test("should display the cards section with cards", async ({ page }) => {
      await page.waitForResponse(
        (response) =>
          response.url().includes("cards.json") && response.status() === 200,
        { timeout: 15000 }
      );

      const cardsContainer = page.locator("#div_cards_all");
      await expect(cardsContainer).toBeVisible({ timeout: 15000 });

      const firstCard = cardsContainer.locator(".cs-card[data-type='card']").first();
      await expect(firstCard).toBeVisible({ timeout: 10000 });

      const firstCardTower = cardsContainer.locator(".cs-card[data-type='tower']").first();
      await expect(firstCardTower).toBeVisible({ timeout: 10000 });

      const cardCount = await cardsContainer.locator(".cs-card").count();
      expect(cardCount).toBeGreaterThan(0);
    });
  });
});
