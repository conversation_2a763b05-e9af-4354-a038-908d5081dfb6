<div class="cs-analysis cs-analysis--default">
    <h2 class="cs-color-GoldenYellow">${result.level == 'intermediate' ? 'Análisis Intermedio' : result.level == 'advanced' ? 'Anális<PERSON> Avanzado' : 'Análisis Básico'}</h2>
    <span class="cs-color-LightGrey">DeckAnalyzer v${result.version}</span>

    <!-- Sección: Vista rápida -->
    <div class="cs-analysis__section cs-analysis__section--default">
        <h3>Vista rápida</h3>
        ${Object.values(result.analysis).map(section => {
            if (section.type == 'data') return '';
            let levelClasses = {
            95: "color-diamond",
            85: "cs-color-VibrantTurquoise",
            75: "cs-color-GoldenYellow",
            65: "color-silver",
            55: "color-silver",
            45: "color-brown",
            35: "color-brown",
            25: "color-brown",
            15: "cs-color-IntenseOrange text-center",
            5: "cs-color-IntenseOrange text-center",
            0: "cs-color-IntenseOrange text-center"
        };
        
        let classNameScore = "";
        Object.entries(levelClasses).forEach(([score, className]) => {
            if (section.score >= score) {
                classNameScore = className;
                return false;
            }
        });
        return `
            <span><span>${section.ui.title}</span> :
                <span class="${classNameScore}">
                    ${section.score}%
                    ${section.ui.score_message ?? ''}
                </span>
                <img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="msgInfo"
                    data-width="12rem" data-inf="${section.ui.description}">
            </span><br>
        `;
        }).join('')}
        <div id="div_view_quik_aditional">
            <span class="cs-color-LightGrey">Arquetipo: </span>
            <span class="${result.analysis.archetype.details.archetypeName === 'Desconocido' ? 'cs-color-IntenseOrange' : 'cs-color-GoldenYellow'}">${result.analysis.archetype.details.archetypeName}</span>
        <img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="info" data-width="20rem" data-inf="${result.analysis.archetype.ui.description ?? 'Información no disponible.'}">
        </div>
        <div class="cs-analysis__sub-section cs-analysis__section--default">
            <span>
                <span class="cs-color-LightGrey">Coste Medio de Elixir: </span>
                <span class="color-elixir">${result.data.averageElixirCost ?? 'N/A'}</span>
                <img class="cs-icon cs-icon--medium" src="./static/media/styles/icons/card_stat_inf/icon_gota_elixir.webp" alt="elixir">
            </span><br>
            <span>
                <span class="cs-color-LightGrey">Ciclo Corto: </span>
                <span class="color-elixir">${result.data.shortCycle ?? 'N/A'}</span>
                <img class="cs-icon cs-icon--medium" src="./static/media/styles/icons/icon_cycle.webp" alt="cycle">
            </span>
        </div>
    </div>

    ${(result.level == 'intermediate' || result.level == 'advanced') ? `
        <h2>Detalles</h2>
        ${Object.entries(result.analysis).map(([keySection, section]) => {
            let sectionHtml = '';
            let suggestionsHtml = '';
            // Define the suggestions HTML once, if available for the current section
            if (section.ui.suggestions && section.ui.suggestions.length > 0) {
                suggestionsHtml = `
                    <div class="cs-analysis__sub-section cs-analysis__section--default">
                        <h3 class="cs-color-GoldenYellow">Sugerencias</h3>
                        ${section.ui.suggestions.map(suggestion => `
                            <p class="m-1 cs-color-GoldenYellow">${suggestion}</p>
                        `).join('')}
                    </div>
                `;
            }

            if (keySection == 'archetype') {
                sectionHtml += `
                    <!-- Sección: Arquetipo -->
                    <h3>Arquetipo<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg"
                            alt="msgInfo" data-width="12rem"
                            data-inf="${section.ui.description}"></h3>
                    <div class="cs-analysis__archetype">
                        <span>Arquetipo: </span>
                        <span class="${section.details.archetypeName === 'Desconocido' ? 'cs-color-IntenseOrange' : 'cs-color-GoldenYellow'}">${section.details.archetypeName}</span>
                        ${(section.details.archetypeName === 'Híbrido' && section.details.matchedArchetypeNames && section.details.matchedArchetypeNames.length > 0) ? `
                            <p><span>Coincide parcialmente con: </span><span class="cs-color-GoldenYellow">${section.details.matchedArchetypeNames.join(', ')}</span></p>
                        ` : ''}
                    </div>
                `;
            } else if (keySection == 'attack') {
                sectionHtml += `
                    <!-- Sección: Ataque -->
                    <h3>Ataque<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg"
                            alt="msgInfo" data-width="12rem"
                            data-inf="${section.ui.description}"></h3>
                    <table class="cs-table cs-table--dark">
                        <thead>
                            <tr>
                                <th>Métrica</th>
                                <th>Valor Promedio</th>
                                <th>Puntaje</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Daño por Segundo (DPS)</td>
                                <td>${section.details.averages.dps.toFixed(2)}</td>
                                <td>${section.details.scores.dps}%</td>
                            </tr>
                            <tr>
                                <td>Daño</td>
                                <td>${section.details.averages.damage.toFixed(2)}</td>
                                <td>N/A</td>
                            </tr>
                            <tr>
                                <td>Puntos de Vida (HP)</td>
                                <td>${section.details.averages.hitpoints.toFixed(2)}</td>
                                <td>${section.details.scores.hitpoints}%</td>
                            </tr>
                            <tr>
                                <td>Velocidad de Ataque</td>
                                <td>${section.details.averages.hitspeed.toFixed(2)}</td>
                                <td>N/A</td>
                            </tr>
                            <tr>
                                <td>Rango</td>
                                <td>${section.details.averages.range.toFixed(2)}</td>
                                <td>${section.details.scores.range}%</td>
                            </tr>
                        </tbody>
                    </table>
                `;
            } else if (keySection == 'defense') {
                sectionHtml += `
                    <!-- Sección: Defensa -->
                    <h3>Defensa<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg"
                            alt="msgInfo" data-width="12rem"
                            data-inf="${section.ui.description}"></h3>
                    <table class="cs-table cs-table--dark">
                        <thead>
                            <tr>
                                <th>Métrica</th>
                                <th>Valor Promedio</th>
                                <th>Puntaje</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Daño por Segundo (DPS)</td>
                                <td>${section.details.averages.dps.toFixed(2)}</td>
                                <td>${section.details.scores.dps}%</td>
                            </tr>
                            <tr>
                                <td>Daño</td>
                                <td>${section.details.averages.damage.toFixed(2)}</td>
                                <td>N/A</td>
                            </tr>
                            <tr>
                                <td>Puntos de Vida (HP)</td>
                                <td>${section.details.averages.hitpoints.toFixed(2)}</td>
                                <td>${section.details.scores.hitpoints}%</td>
                            </tr>
                            <tr>
                                <td>Velocidad de Ataque</td>
                                <td>${section.details.averages.hitspeed.toFixed(2)}</td>
                                <td>N/A</td>
                            </tr>
                            <tr>
                                <td>Rango</td>
                                <td>${section.details.averages.range.toFixed(2)}</td>
                                <td>${section.details.scores.range}%</td>
                            </tr>
                        </tbody>
                    </table>
                `;
            } else if (keySection == 'weaknesses' && (result.level == 'advanced' || result.level == 'intermediate')) {
                sectionHtml += `
                    <!-- Sección: Cobertura de Defensa -->
                    <h3>Cobertura de Defensa<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg"
                            alt="msgInfo" data-width="12rem"
                            data-inf="${section.ui.description}"></h3>
                    <table class="cs-table cs-table--dark">
                        <thead>
                            <tr>
                                <th>Tipo de Defensa</th>
                                <th>Cobertura</th>
                                <th>Cartas que Aportan</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(section.details.defenseCoverage).map(([type, coverageInfo]) => `
                                <tr>
                                    <td>
                                        ${type}
                                    </td>
                                    <td>${coverageInfo.isCovered ? '✅' : '❌'}</td>
                                    <td>
                                        <div class="flex m-1">
                                            ${coverageInfo.isCovered && coverageInfo.cards && coverageInfo.cards.length > 0 ? `
                                                ${coverageInfo.cards.map(card => `
                                                    <div class="cs-card cs-card--small">
                                                        <img class='cs-card__image' src="${card.evolvedInDeck ? localStorage.getItem('default_url_cards') + (card.id + 10000000) + '.webp' : localStorage.getItem('default_url_cards') + card.id + '.webp'}" alt="${card.name}">
                                                    </div>
                                                `).join('')}
                                            ` : `
                                                <span>N/A</span>
                                            `}
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            } else if (keySection == 'versatility' && result.level == 'advanced') {
                const cardTypes = ['winConditions', 'terrestrials', 'aerials', 'keyDefenses', 'spells'];
                sectionHtml += `
                    <!-- Sección: Detalles de Versatilidad -->
                    <h3>Versatilidad <img class="cs-tooltip-image"
                            src="./static/media/styles/icons/info-circle.svg" alt="msgInfo" data-width="12rem"
                            data-inf="${section.ui.description}">
                    </h3>
                    <div class="cs-analysis__sub-section cs-analysis__section--default">
                        <h3>Cartas por Tipo</h3>
                        <table class="cs-table cs-table--dark">
                            <thead>
                                <tr>
                                    <th>Condición de Victoria</th>
                                    <th>Terrestre</th>
                                    <th>Aéreo</th>
                                    <th>Defensa Clave</th>
                                    <th>Hechizos</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    ${cardTypes.map((typeCard) => {
                                        return `
                                            <td>
                                            ${section.details.groupedCards[typeCard].map((card) => `
                                                <div class='cs-card cs-card--small'><img class='cs-card__image' src='${card ? card.evolvedInDeck ? localStorage.getItem('default_url_cards') + (card.id + 10000000) + '.webp' : localStorage.getItem('default_url_cards') + card.id + '.webp' : "./static/media/styles/icons/icon_card_denegado.webp"}' alt='card_image'></div>
                                            `).join('')}
                                            </td>
                                    `}).join('')}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;
            } else if (keySection == 'synergy' && result.level == 'advanced') {
                sectionHtml += `
                    <!-- Sección: Detalles de sinergia -->
                    <h3>Sinergias<img class="cs-tooltip-image" src="./static/media/styles/icons/info-circle.svg" alt="msgInfo"
                            data-width="12rem" data-inf="${section.ui.description}"></h3>
                    
                    <!-- Sección: Componentes de Puntaje -->
                    <div class="cs-analysis__sub-section cs-analysis__section--default">
                        <h3>Componentes de Puntaje</h3>
                        <p class="m-1"><span>Calidad: </span>${section.details.scoringComponents.quality}%</p>
                        <p class="m-1"><span>Cobertura: </span>${section.details.scoringComponents.coverage}%</p>
                        <p class="m-1"><span>Total de Puntos Actuales: </span>${section.details.scoringComponents.points.achieved}</p>
                        <p class="m-1"><span>Total de Puntos Máximos: </span>${section.details.scoringComponents.points.possible}</p>
                        <p class="m-1"><span>Total de Puntos Positivos: </span>${section.details.scoringComponents.points.positive}</p>
                        <p class="m-1"><span>Total de Puntos Negativos: </span>${section.details.scoringComponents.points.negative}</p>
                    </div>

                    <!-- Sección: Top Sinergias Positivas -->
                    ${section.details.topPositiveSynergies ? `
                        <div class="cs-analysis__sub-section cs-analysis__section--default">
                            <h3 class="cs-color-GoldenYellow">Sinergias Clave Positivas</h3>
                                ${Object.values(section.details.topPositiveSynergies).map(synergy => `
                                    <p class="m-1">
                                        <span>${synergy.context}:</span>
                                        <span class="cs-color-VibrantTurquoise text-center">+${synergy.points.toFixed(2)} pts</span>
                                    </p>
                                `).join('')}
                        </div>
                    ` : ''}

                    <!-- Sección: Top Sinergias Negativas -->
                    ${section.details.topNegativeSynergies ? `
                        <div class="cs-analysis__sub-section cs-analysis__section--default">
                            <h3 class="cs-color-GoldenYellow">Puntos Débiles de Sinergia</h3>
                                ${Object.values(section.details.topNegativeSynergies).map(synergy => `
                                    <p class="m-1">
                                        <span>${synergy.context}:</span>
                                        <span class="cs-color-IntenseOrange text-center">${synergy.points.toFixed(2)} pts</span>
                                    </p>
                                `).join('')}
                        </div>
                    ` : ''}

                    <!-- Sección: Sinergia General -->
                    <div class="cs-analysis__sub-section cs-analysis__section--default cards__details">
                        <h3 class="cs-color-GoldenYellow">Sinergia de Estrategia</h3>
                        <p class="cs-color-LightGrey">${section.details.synergyPoints.strategy.achieved ?? 0} / ${section.details.synergyPoints.strategy.possible ?? 0} Puntos de Sinergia</p>
                        <div class="m-1">
                            <h3 class="cs-color-GoldenYellow">General (Mazo Completo)</h3>
                            ${section.details.strategySynergies.general ? `
                                ${section.details.strategySynergies.general.map(synergy => `
                                    <p class="m-1">
                                        <span>${synergy.name}: +${synergy.points} pts - ${synergy.message}</span>
                                    </p>
                                `).join('')}
                            ` : `
                                <p class="m-1">No hay sinergias generales</p>
                            `}
                        </div>
                        <h3 class="cs-color-GoldenYellow">Por Carta (Individual)</h3>
                        <div class="m-1 flex">
                            ${(section.details.strategySynergies.individual && Array.isArray(section.details.strategySynergies.individual) && section.details.strategySynergies.individual.length > 0)
                                ? section.details.strategySynergies.individual.map(strategyCard => `
                                    <div class="cs-card cs-deck__card--medium">
                                        <img class="cs-card__image cs-card__image--medium" src="${strategyCard.evolvedInDeck ? localStorage.getItem('default_url_cards') + (strategyCard.id + 10000000) + '.webp' : localStorage.getItem('default_url_cards') + strategyCard.id + '.webp'}" alt='${strategyCard.name}'>
                                        ${(() => {
                                            let colorClass = '';
                                            if (strategyCard.points > 10) {
                                                colorClass = 'cs-color-VibrantTurquoise';
                                            } else if (strategyCard.points > 5) {
                                                colorClass = 'cs-color-GoldenYellow';
                                            } else if (strategyCard.points >= 0) {
                                                colorClass = 'cs-color-IntenseOrange';
                                            }
                                            return `<span class="span_infver ${colorClass}">+${strategyCard.points} / ${strategyCard.maximumPoints ?? 0} pts</span>`;
                                        })()}
                                        <img class="cs-analysis-sinergy-cards__toltip cs-tooltip-image"
                                            src="./static/media/styles/icons/info-circle.svg" alt="inf" data-width="15em"
                                            data-overflow="true" data-inf='<ul class="cs-list cs-list--small">${
                                            strategyCard.reasons ?
                                                strategyCard.reasons.map(reason => `
                                                     <li><strong>${reason.name}</strong>: ${reason.points >= 0 ? "+" : ""}${reason.points} pts - ${reason.message}</li>
                                                `).join("")
                                            : `<li>No hay detalles específicos.</li>`
                                            }</ul>'>
                                    </div>
                                `).join('')
                                : '<div class="cs-color-LightGrey text-center">No hay sinergias de estrategia disponibles</div>'
                            }
                        </div>
                    </div>
                    
                    <!-- Sección: Sinergias de Cartas -->
                    <div class="cs-analysis__sub-section cs-analysis__section--default">
                        <h3 class="cs-color-GoldenYellow">Sinergias de Cartas</h3>
                        <p class="cs-color-LightGrey">${section.details.synergyPoints.card.achieved ?? 0} / ${section.details.synergyPoints.card.possible ?? 0} Puntos de Sinergia</p><br>
                        ${(section.details.cardSynergies && Array.isArray(section.details.cardSynergies) && section.details.cardSynergies.length > 0)
                            ? section.details.cardSynergies.map(synergyCard => `
                                <div class="cs-analysis-sinergy-cards">
                                    <div class="cs-analysis-sinergy-cards__card">
                                        <div class="cs-card">
                                            ${(() => {
                                                let colorClass = '';
                                                if (synergyCard.points > 10) {
                                                    colorClass = 'cs-color-VibrantTurquoise';
                                                } else if (synergyCard.points > 5) {
                                                    colorClass = 'cs-color-GoldenYellow';
                                                } else if (synergyCard.points >= 0) {
                                                    colorClass = 'cs-color-IntenseOrange';
                                                }
                                                return `<span class="span_infver ${colorClass}">+${synergyCard.points ?? 0} / ${synergyCard.maximumPoints ?? 0} pts</span>`;
                                            })()}
                                            <img class='cs-card__image' src="${synergyCard.evolvedInDeck ? localStorage.getItem('default_url_cards') + (synergyCard.id + 10000000) + '.webp' : localStorage.getItem('default_url_cards') + synergyCard.id + '.webp'}" alt='${synergyCard.name}'>
                                        </div>
                                        <img class="cs-analysis-sinergy-cards__img_aumento"
                                            src="./static/media/styles/icons/icon_aumento.webp" alt="aumento">
                                    </div>
                                    <div class="cs-analysis-sinergy-cards__cards">
                                        ${synergyCard.points <= 0 ? `
                                            <div class="cs-card">
                                                <img class="card__not-found" src="./static/media/styles/icons/icon_card_denegado.webp">
                                            </div>
                                        ` : `
                                            ${(synergyCard.synergisticCards && Object.keys(synergyCard.synergisticCards).length > 0)
                                                ? Object.values(synergyCard.synergisticCards).map(synergyCardSynergy => {
                                                if(synergyCardSynergy.points == 0) {
                                                    return '';
                                                }
                                                return `
                                                    <div class="cs-card">
                                                        <img class='cs-card__image' src="${synergyCardSynergy.evolvedInDeck ? localStorage.getItem('default_url_cards') + (synergyCardSynergy.id + 10000000) + '.webp' : localStorage.getItem('default_url_cards') + synergyCardSynergy.id + '.webp'}" alt='${synergyCardSynergy.name}'>
                                                        <span class="span_infver">+${synergyCardSynergy.points} / ${synergyCardSynergy.maximumPoints ?? 0} pts</span>
                                                        <img class="cs-analysis-sinergy-cards__toltip cs-tooltip-image"
                                                            src="./static/media/styles/icons/info-circle.svg" alt="inf" data-width="15em"
                                                            data-overflow="true"
                                                            data-inf='<ul class="cs-list cs-list--small">${
                                                            synergyCardSynergy.reasons ?
                                                                synergyCardSynergy.reasons.map(reason => `
                                                                     <li><strong>${reason.name}</strong>: ${reason.points >= 0 ? "+" : ""}${reason.points} pts - ${reason.message}</li>
                                                                `).join("")
                                                            : `<li>No hay detalles específicos.</li>`
                                                            }</ul>'>
                                                    </div>
                                                `}).join('')
                                                : '<div class="cs-color-LightGrey text-center">No hay cartas de sinergia disponibles</div>'
                                            }
                                        `}
                                    </div>
                                </div>
                            `).join('')
                            : '<div class="cs-color-LightGrey text-center">No hay sinergias de cartas disponibles</div>'
                        }
                    </div>
                `;
            }

            // Append suggestionsHtml if it exists and wrap the entire sectionHtml in its main div
            if (sectionHtml) {
                sectionHtml = `
                    <div class="cs-analysis__section cs-analysis__section--default">
                        ${sectionHtml}
                        ${suggestionsHtml}
                    </div>
                `;
            }
            return sectionHtml;
        }).join('')}

        <script>
            Config.addSlick("img", $('.cs-analysis-sinergy-cards__cards'), 3);
        </script>
    ` : ''}
</div>
