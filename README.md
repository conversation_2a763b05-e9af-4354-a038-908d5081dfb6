# Clash Strategic™ Web Application

<div align="center">
  <img src="./static/media/styles/logo/logo_banner.webp" alt="Clash Strategic Logo" width="400">
  
  [![Version](https://img.shields.io/badge/version-1.2.1-1B263B.svg)](https://github.com/ClashStrategic/webapp/releases)
  [![License](https://img.shields.io/badge/license-Apache%202.0-36CFC9.svg)](https://github.com/ClashStrategic/webapp/blob/main/LICENSE)
  [![PWA](https://img.shields.io/badge/PWA-In%20Development-FFC107.svg)](https://web.dev/progressive-web-apps/)
  [![Semantic Release](https://img.shields.io/badge/%20%20%F0%9F%93%A6%F0%9F%9A%80-semantic--release-FF5722.svg)](https://github.com/semantic-release/semantic-release)
  [![Contributions Welcome](https://img.shields.io/badge/contributions-welcome-A8E6CF.svg)](https://github.com/ClashStrategic/webapp/blob/main/CONTRIBUTING.md)
</div>

## 📑 Table of Contents

- [About](#-about)
- [Key Features](#-key-features)
- [Architecture](#-architecture)
- [Live Demo](#-live-demo)
- [Contributing](#-contributing)
- [License](#-license)
- [Support & Contact](#-support--contact)
- [Acknowledgments](#-acknowledgments)

## 📱 About

**Clash Strategic™** is a comprehensive Progressive Web Application (PWA) designed for the Clash Royale community. It provides strategic tools, deck building capabilities, card analysis, and community features to help players improve their gameplay and connect with other strategists.

### 🌟 Key Features

- **🏗️ Advanced Deck Builder**: Create, analyze, and optimize your Clash Royale decks
- **📊 Deck Analysis**: Get detailed statistics including elixir cost, cycle analysis, and strategic insights
- **🃏 Card Management**: Browse and manage all Clash Royale cards with detailed statistics
- **👥 Community Hub**: Connect with other players through chat and publications
- **📱 PWA Support**: Install as a native app on mobile and desktop devices
- **🔄 Real-time Updates**: Automatic content updates and notifications
- **🎮 Game Integration**: Direct deck copying to Clash Royale
- **🔐 Secure Authentication**: Google OAuth and guest access support
- **🌐 Offline Support**: Service worker enables offline functionality
- **📈 Performance Optimized**: Fast loading with intelligent caching

## 🏛️ Architecture

### Image Caching Strategy

To optimize performance and reduce loading times, **Clash Strategic™** employs an intelligent image caching strategy. Instead of storing card images locally within the project, the application fetches them from a dedicated external API (`https://humbleapi.galacticapricot.workers.dev/images/cards/full_b/`).

This approach offers several key advantages:

- **Reduced Initial Load**: By fetching images on-demand, the initial size of the application is significantly smaller, leading to a faster first-time load.
- **Efficient Caching**: The service worker (`sw.js`) dynamically caches these images. On the first visit, it fetches the `cards.json` file to get a list of all card images and then caches them in the background.
- **Seamless Updates**: When new cards are added to the game, updating the `cards.json` file is all that's needed. The service worker will automatically fetch and cache the new images without requiring a full application update.
- **Improved Performance**: Once cached, the images are served directly from the local cache, providing a smooth and fast user experience, even on slower network connections.

This architecture ensures that the application remains lightweight and scalable, while still delivering a rich and responsive user experience.

## 🚀 Live Demo

Visit the live application: <a href="https://clashstrategic.great-site.net" target="_blank" rel="noopener noreferrer">https://clashstrategic.great-site.net</a>

## 🤝 Contributing

We welcome contributions from developers of all skill levels!

For detailed guidelines, setup instructions, and development tips, see our [**Contributing Guide**](CONTRIBUTING.md).

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Contact

- **Issues**: [GitHub Issues](https://github.com/ClashStrategic/webapp/issues)
- **Discussions**: [GitHub Discussions](https://github.com/ClashStrategic/webapp/discussions)
- **Website**: [https://clashstrategic.great-site.net](https://clashstrategic.great-site.net)

## 🙏 Acknowledgments

- **Clash Royale** by Supercell for the amazing game
- **Community Contributors** who help improve the application
- **Open Source Libraries** that make this project possible

---

<div align="center">
  <p>💖 Thank you for being part of the Clash Strategic community</p>
  <p>
    <a href="#top">Back to Top ⬆️</a>
  </p>
</div>
