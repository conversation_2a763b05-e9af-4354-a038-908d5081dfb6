.cs-card {
    position: relative;
    aspect-ratio: 9/11;
}

.cs-card__elixir {
    position: absolute;
    background-color: var(--elixir);
    border-bottom-left-radius: 35px;
    border-bottom-right-radius: 40px;
    border-top-left-radius: 40px;
    top: 0;
    left: -5px;
    width: 23px;
    height: 24.8px;
    text-align: center;
}

.cs-card__tag-evo {
    position: absolute;
    background-color: var(--dark-elixir);
    padding: 0.15rem;
    top: -0.5em;
    left: 50%;
    transform: translate(-50%, 0%);
    font-size: var(--fs-small);
}

.cs-card--small {
    width: 3rem;
}

.cs-card--medium {
    width: 5rem;
}

.cs-card--large {
    width: 8rem;
}

.cs-card--model-fixed {
    position: fixed;
    left: 50%;
    bottom: 67%;
    transform: translate(-50%);
}

.cs-card__image {
    width: 100%;
    filter: brightness(105%);
}

.cs-card__name {
    position: absolute;
    background: var(--color-dark-neutral-25);
    font-size: var(--fs-small);
    left: 0%;
    right: 0%;
    top: 1.5em;
    word-wrap: break-word;
    text-shadow: 1px 1px var(--cs-color-DeepBlue);
}

.cs-card__options {
    width: 100%;
    text-align: center;
}

.cs-card__info {
    background-color: var(--cs-color-GoldenYellow);
    width: 95%;
    height: 2.5em;
    box-shadow: 1px 1px 1px var(--cs-color-DeepBlue);
    color: var(--cs-color-DeepBlue);
}

.cs-card__use-remove {
    background-color: var(--cs-color-GoldenYellow);
    width: 95%;
    height: 2.5em;
    margin-top: 0.5em;
    box-shadow: 1px 1px 1px var(--cs-color-DeepBlue);
    color: var(--cs-color-DeepBlue);
}

/* card space object */

.cs-card-space {
    position: relative;
    display: inline-block;
    aspect-ratio: 9/11;
    width: 24%;
}

.cs-card-space .cs-card {
    position: absolute;
    width: 100%;
}

/* collection decks object */

.cs-deck-collection {
    background: var(--cs-color-DeepBlue);
    box-shadow: 0px 0px 5px var(--cs-color-DeepBlue);
    text-align: center;
    width: 100%;
    display: inline-block;
}

.cs-deck-collection__box-btns {
    display: inline-flex;
    align-items: center;
    margin: 0.5em;
}

.cs-deck-collection__box-btns #div_maz_1_5,
.cs-deck-collection__box-btns #div_maz_6_10 {
    display: inline-flex;
    align-self: center;
}

.cs-deck-collection__box-btns-option {
    background: transparent;
    border: solid 1.5px var(--cs-color-LightGrey);
    margin: 0.25em;
    padding: 0.25em 0.5em;
    cursor: pointer;
}

.cs-deck-collection__box-btns-option:hover {
    transform: scale(1.1);
}

.cs-deck-collection__box-btns-option span {
    color: var(--cs-color-LightGrey);
}

.cs-deck-collection__alert {
    margin: 0.5em;
}

.btn_opc_cre_maz {
    background: transparent;
    border: solid 1.5px var(--cs-color-LightGrey);
    margin: 0.25em;
    padding: 0.25em 0.5em;
    cursor: pointer;
}

.selectmaz {
    background: var(--cs-color-GoldenYellow) !important;
}

.selectmaz span {
    color: var(--cs-color-DeepBlue);
}

/* specific styles */

#deck-data-info {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.25rem;
}

#deck-data-info div {
    display: flex;
    align-items: center;
}

#div_btns_mazo {
    position: relative;
    width: 93%;
    height: 4em;
    margin: 0.5em;
    margin-left: 0.5em;
}

#div_btns_pri {
    position: absolute;
    width: 10em;
    height: 3.25em;
    left: 30%;
    top: 0.25em;
}

#div_btns_pri button {
    margin: 0;
}

.btn_select_opc_cre {
    background: var(--cs-color-GoldenYellow);
    color: var(--cs-color-DeepBlue);
}

#deck-tool-forms__logs {
    background: var(--cs-color-DeepBlue);
    text-align: center;
    margin: 0.5rem 0;
}

#div_crear_mazo,
#div_analizar_mazo {
    background: var(--cs-color-DeepBlue);
    text-align: center;
    display: inline-block;
    border: solid 1.5px var(--cs-color-LightGrey);
    width: 100%;
}

#div_opc_cre_maz {
    display: flex;
    align-items: center;
    margin-left: 0.75em;
}

#btn_ver_log_maz {
    padding: 0.5em;
    color: var(--cs-color-DeepBlue);
    font-size: var(--fs-x-small);
}

#div_log_maz {
    border: solid 1.5px var(--cs-color-DeepBlue);
    height: 7em;
    overflow-x: auto;
    border: solid 1px var(--cs-color-DeepBlue);
    margin: auto 0.75em;
    text-align: left;
    color: var(--Plata);
    font-size: var(--fs-x-small);
}

#div_opc_cre_maz span {
    font-size: var(--fs-small);
}

#div_mis_est,
#div_otr_est {
    margin: 0 auto;
    width: 100%;
}

.div_sistem_card {
    position: relative;
    width: 100%;
}

.fs_iter_cards {
    position: relative;
    border: solid 1.5px var(--cs-color-LightGrey);
}

.btn_add_iter_cards {
    width: 98%;
    margin: 0;
    margin-top: -0.25rem;
}

.fs_condition {
    position: relative;
    border: solid 1.5px var(--cs-color-LightGrey);
    margin: 0.5em;
}

.fs_pov {
    position: relative;
    border: solid 1.5px var(--cs-color-DeepBlue);
    background: var(--cs-color-LightGrey);
    margin: 0.25em;
}

.btn_add_condition {
    width: 96%;
    margin: -0.5rem 0 0 0;
}

.sel_points_condition {
    width: 20%;
}

.inp_msg_condition {
    width: 70%;
    font-size: var(--fs-small);
}

.btn_add_pov {
    width: 97%;
    margin: -0.5rem 0 0.25rem 0;
}

.btn_eliminar_con_pov {
    background: var(--cs-color-IntenseOrange) !important;
    padding: 0.25em !important;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    width: 1.5em;
    height: 1.5em;
    text-align: center;
    align-content: center;
}

.div_apply_est {
    background: var(--cs-color-DeepBlue);
    position: relative;
    display: inline-block;
    border: solid 1.5px var(--cs-color-LightGrey);
    text-align: center;
    margin: 0.5rem 0;
    width: 100%;
}

.p_nombre_estrategia {
    padding: 0.15em;
    width: 90%;
    margin: 0.75em auto;
    font-size: var(--fs-large);
}

.p_descripcion_estrategia {
    padding: 0.15em;
    width: 90%;
    margin: 0.5em auto;
    font-size: var(--fs-small);
    color: var(--cs-color-LightGrey);
}

.div_show_condicion {
    border: solid 1.5px var(--cs-color-DeepBlue);
    margin: 0.75em;
}

.details_show_tog_con {
    background: var(--cs-color-LightGrey);
    border: solid 1.5px var(--cs-color-DeepBlue);
    margin: 0.5em;
    padding: 0.5em;
    font-size: var(--fs-small);
}

#div_tog_gen_con {
    align-content: center;
}

.div_show_pov,
.span_iter_show,
.span_operator_show_est {
    display: inline-block;
    background: var(--cs-color-IntenseOrange) !important;
    position: relative;
    background: var(--cs-color-LightGrey);
    padding: 0.25rem;
    white-space: nowrap;
    border: solid 1px var(--cs-color-DeepBlue);
    margin: 0.5rem;
    color: var(--cs-color-DeepBlue);
}

.span_type_comparator {
    position: absolute;
    background: var(--cs-color-GoldenYellow);
    font-size: var(--fs-x-small);
    border: solid 1px var(--cs-color-DeepBlue);
    top: -0.5rem;
    left: -0.5rem;
}

.btn_edit_est {
    position: absolute;
    top: 0.25em;
    right: 0.25em;
}

.apply_est {
    filter: opacity(0.75);
}

.div_apply_con {
    display: inline-block;
    background: var(--cs-color-VibrantTurquoise-1);
    margin: 0.5em;
    width: 95%;
}

.div_estrategias_seleccionadas {
    border: solid 1.5px var(--cs-color-DeepBlue);
}

#div_est_cre,
#div_est_ana {
    border: solid 1.5px var(--cs-color-LightGrey);
    margin: 0.5em;
    padding: 0.5em;
}

#btn_btns_opc_maz {
    position: absolute;
    padding: 0.5rem;
    right: 0%;
    border: 1px solid var(--cs-color-DeepBlue);
    top: 0.25rem;
}

#div_sub_btns_mazo {
    background: var(--cs-color-LightGrey);
    box-shadow: 0 0 5px var(--cs-color-DeepBlue);
    position: absolute;
    display: inline;
    right: 3.5rem;
    top: 0.25rem;
    align-items: center;
    z-index: 80;
    width: 9rem;
}

#div_sub_btns_mazo::before {
    content: "";
    position: absolute;
    top: 1em;
    right: -20px;
    border: 10px solid transparent;
    border-left: 10px solid var(--cs-color-LightGrey);
}

#div_sub_btns_mazo button {
    margin: 0.5em;
    display: flex;
    align-items: center;
    width: 90%;
}

#div_inf_mazo {
    border: solid 1.5px var(--cs-color-LightGrey);
    width: 95%;
    margin: auto;
}

.div_tricks_ana {
    display: flex;
    justify-content: center;
    align-items: center;
}

.video_tricks_ana,
.video_strategies_ana {
    max-width: 95%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 1/1;
}

.div_tricks,
.div_est {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 100%;
}

.div_tvd {
    display: inline;
    max-width: 50%;
}

.video_tricks {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 1/1;
}

#div_res_ana_maz {
    width: 100%;
    background: var(--cs-color-DeepBlue);
    display: inline-block;
}

.img_loading {
    position: absolute;
    background: transparent;
    height: 75%;
    aspect-ratio: 1/1;
    margin: 0;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 90;
}

.div_syn_img_mas {
    display: flex;
    width: 40%;
}

.span_infver {
    font-size: var(--fs-small);
    background: var(--color-light-neutral-25);
    width: 75%;
    padding: 0.15rem;
    text-shadow: 1px 1px var(--cs-color-DeepBlue);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

#div_res_ST {
    display: none;
    width: 100%;
    background: var(--cs-color-DeepBlue);
    margin: 2.5rem 0;
}