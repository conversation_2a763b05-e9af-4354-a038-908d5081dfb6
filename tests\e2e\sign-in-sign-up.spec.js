/**
 * @file End-to-end tests for the sign-in and sign-up functionality. This suite verifies
 * that the sign-in and sign-up views are correctly rendered and that navigation between
 * them works as expected. It also checks for the presence of essential UI elements
 * like titles and Google authentication buttons.
 * @group Authentication
 */
import { test, expect } from "@playwright/test";

/**
 * @description Test suite for the sign-in and sign-up user flow.
 */
test.describe("Clash Strategic - Sign-In and Sign-Up Flow", () => {
    let consoleMessages;

    /**
     * @description Captures console messages before each test to monitor for errors.
     */
    test.beforeEach(async ({ page }) => {
        consoleMessages = [];
        page.on("console", (msg) => {
            consoleMessages.push(msg);
        });
        await page.goto("/home.html");
        await page.waitForLoadState("domcontentloaded");

        await page.waitForResponse(
            (response) =>
                response.url().includes("HomeView") && response.status() === 200,
            { timeout: 15000 }
        );

        // Close the welcome modal if it appears
        const welcomeModal = page.locator("#div_tog_gen");
        if (await welcomeModal.isVisible()) {
            await welcomeModal.locator(".btn_x_perfil").click();
            await expect(welcomeModal).toBeHidden();
        }
    });

    /**
     * @description Checks for console errors after each test, failing if any are found.
     * Warnings are logged but do not cause the test to fail.
     */
    test.afterEach(async () => {
        const errors = consoleMessages.filter((msg) => msg.type() === "error");
        const warnings = consoleMessages.filter((msg) => msg.type() === "warn");

        if (warnings.length > 0) {
            console.warn(`\nFound ${warnings.length} console warnings:`);
            warnings.forEach((w) => {
                const loc = w.location();
                console.warn(
                    `- ${w.text()} (at ${loc.url}:${loc.lineNumber}:${loc.columnNumber})`
                );
            });
        }

        if (errors.length > 0) {
            const errorDetails = errors
                .map((e) => {
                    const loc = e.location();
                    return `${e.text()} (at ${loc.url}:${loc.lineNumber}:${loc.columnNumber
                        })`;
                })
                .join("\n- ");
            const errorMessage = `Test failed due to ${errors.length} console errors:\n- ${errorDetails}`;
            expect(errors, errorMessage).toHaveLength(0);
        }
    });

    /**
     * @description Main test case for verifying the sign-in and sign-up navigation flow.
     */
    test("should navigate between sign-in and sign-up views", async ({ page }) => {
        await page.click("#btn_menu_opc");
        await expect(page.locator("#menu_opciones")).toBeVisible();
        await page.click('#btn_sign_in');
        await expect(page.locator("#div_tog_gen")).toBeVisible();
        await expect(page.locator("#div_tog_gen_con")).not.toBeEmpty();
        await expect(page.locator(".g_id_signin")).toBeVisible();

        await page.click('#a_register_link');
        await expect(page.locator("#div_tog_gen")).toBeVisible();
        await expect(page.locator("#div_tog_gen_con")).not.toBeEmpty();
        await expect(page.locator(".g_id_signin")).toBeVisible();
    });
});