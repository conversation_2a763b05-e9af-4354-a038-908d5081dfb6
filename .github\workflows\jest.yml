name: Jest Tests
on:
  pull_request:
    branches: [ main ]
jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "lts/*"
      - name: Install dependencies
        run: npm ci
      - name: Run Jest tests
        run: npm test
      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: jest-report
          path: coverage/
          retention-days: 30
