# [1.8.0](https://github.com/ClashStrategic/webapp/compare/v1.7.0...v1.8.0) (2025-10-01)


### Bug Fixes

* **sw:** gracefully handle cards.json fetch error in service worker (Thanks to [@Gamalie<PERSON>](https://github.com/Gamaliel)) efb2c7adbd2f07dbb12b02c73487c567ab54bf4c



### Features

* **sw:** pre-cache card images using Humble API data (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 769d4e093d1837a0efe49a7020b9d3a4e6bb0f9a

# [1.7.0](https://github.com/ClashStrategic/webapp/compare/v1.6.2...v1.7.0) (2025-09-29)


### Features

* **stats:** add @clash-strategic/stats package (Thanks to [@G<PERSON><PERSON><PERSON>](https://github.com/Gamaliel)) 60f28693decca66551f6af6e084e56ef76be8742

* **stats:** fetch card data from local package (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 125feb695f204c8ee3bc6ddf6febecd1cd8d6cfe

## [1.6.2](https://github.com/ClashStrategic/webapp/compare/v1.6.1...v1.6.2) (2025-09-19)


### Bug Fixes

* **deck:** auto-save selected deck when using deckbuilder (Thanks to [@Gamaliel](https://github.com/Gamaliel)) f389b03f55fb6909921132ed4e1f989c64434cf9

## [1.6.1](https://github.com/ClashStrategic/webapp/compare/v1.6.0...v1.6.1) (2025-09-12)


### Bug Fixes

* **card:** correct spell card DPS calculation and type comparison (Thanks to [@Gamaliel](https://github.com/Gamaliel)) da8b3601524a16e4639314920718d4b8dd18dbcd

* **shop:** prevent anonymous access to in-game currency market (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 9efa6a529927dd1526253e9ed47f0b3b971c01ea

* **stats-card-view:** fixes large card model image when screen is large (Thanks to [@Gamaliel](https://github.com/Gamaliel)) c826fc9ade7b7f9dd48529e63f2dfafc2cf5fecc

# [1.6.0](https://github.com/ClashStrategic/webapp/compare/v1.5.1...v1.6.0) (2025-09-12)


### Bug Fixes

* **legacy-deck-tools:** warn users about instability in older deck interfaces (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 2592586af16e5d5cddac71205acd5bd87ae38bac



### Features

* **deck-analysis:** adapt v1-beta view to updated analysis data model (Thanks to [@Gamaliel](https://github.com/Gamaliel)) f9c5e613b2bbbaefb175d86053abd318353bde8d

* **deck-pricing:** update gem costs for deck features (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 34c681122cc94fdb6b3e2e782b63d60f2b69ae87

## [1.5.1](https://github.com/ClashStrategic/webapp/compare/v1.5.0...v1.5.1) (2025-08-29)


### Bug Fixes

* **deck:** rename level field to algorithm for v1-beta compatibility (Thanks to [@Gamaliel](https://github.com/Gamaliel)) bbf30438785a46bbfb115a5a157cc93871b0b7ae

# [1.5.0](https://github.com/ClashStrategic/webapp/compare/v1.4.0...v1.5.0) (2025-08-28)


### Bug Fixes

* **deck:** add card type validation to prevent incorrect method usage (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 9a5f2c898c8ac1492053e86a0d98de45e0519037

* **deck:** add check for tower card existence (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 721ca0b404cc69d63844b2034dd6e7a210b929f1

* **deck:** correct win condition detection and standardize rarity values (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 1318e5ed3f43c3a83ee1e50a0da361eb26abe716



### Features

* **api:** add specific error handlers for enhanced error responses (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 6af9ab497841c538c540fd945fc0dbbda64341a3

* **beta:** add feedback messaging for beta tools (Thanks to [@Gamaliel](https://github.com/Gamaliel)) dfbdba2995e8e5c4da3f49ba739a8ee8d9321eaf

* **deck-tools:** add version support for analysis and builder tools (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 83bd7a2530bba861261ff91baccc01e614f94dfa

# [1.4.0](https://github.com/ClashStrategic/webapp/compare/v1.3.0...v1.4.0) (2025-07-15)


### Features

* **card-assets:** add inferno dragon evolution card image (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 2f517c6ac3148906767a89eb715c5cf2edbbff59

* **card-assets:** add skeleton barrel evolution card image (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 988f51f5ab93eb2c5d846e1bc3eb4724fcfc156b

* **card-assets:** add spirit empress card image (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 35b4ff34050269f1a148bc50c1f232465a229337

* **card-assets:** add witch evolution card image (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 1365c6d6886b06b9603b796fc328ee1c72d06b61

# [1.3.0](https://github.com/ClashStrategic/webapp/compare/v1.2.4...v1.3.0) (2025-07-13)


### Bug Fixes

* **card-stats:** correct dps calculation (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 038104f7020733d9e35bcfd559ef3c0db813f4e7

* **card:** replace dynamic image URLs with static paths (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 7cc96455211065d0f493a36e04995cbb8c39f418

* **description-cards:** fix stats card description fallback logic (Thanks to [@Gamaliel](https://github.com/Gamaliel)) a5d7aab0a45ac89138c9b117177be1d69a96d6c6



### Features

* **card-stats:** display new card statistics in detail view (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 4b5c8927ffb9d3bcce3546db9b75e90c1fca37b8

* **stat-cards:** Get new data from the new endpoint to the "v1/stats/cards" API (Thanks to [@Gamaliel](https://github.com/Gamaliel)) b3785c1e8a0eb9766ef4434a643e92efe1f9410b

* **stat-cards:** prepare and pass new stats to template (Thanks to [@Gamaliel](https://github.com/Gamaliel)) cab28097c0dacfc04e3a1b3771b01e21932be7d2

* **ui:** add image fallback for card assets (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 5d8848ea14ce7d60127edb03af4698eec7b10fd0

* **ui:** use new stats and static image paths for cards (Thanks to [@Gamaliel](https://github.com/Gamaliel)) a30bc3b8e712d47b5667f54c107e111a67226bb8

## [1.2.4](https://github.com/ClashStrategic/webapp/compare/v1.2.3...v1.2.4) (2025-07-10)


### Bug Fixes

* **auth:** clear specific local storage items on logout (Thanks to [@Gamaliel](https://github.com/Gamaliel)) 041dca665efaa799f8048558fb2f8dc6fa3403b4

* **ui:** correct typo in logout prompt (Thanks to [@Gamaliel](https://github.com/Gamaliel)) e40f25419cdd68c6b21d76df5dda5011772446fb

## [1.2.3](https://github.com/ClashStrategic/webapp/compare/v1.2.2...v1.2.3) (2025-07-01)


### Bug Fixes

* **config:** ensure current api base url ([74d2844](https://github.com/ClashStrategic/webapp/commit/74d2844d05819448a795db79fa98a00c32267824))

## [1.2.2](https://github.com/ClashStrategic/webapp/compare/v1.2.1...v1.2.2) (2025-07-01)


### Bug Fixes

* **api:** ensure credentials sent with cross-origin requests ([4132f17](https://github.com/ClashStrategic/webapp/commit/4132f17f6c9a1691cf81b5e8886bb99603d2d40a))
* **config:** centralize api base url configuration ([3d1bb99](https://github.com/ClashStrategic/webapp/commit/3d1bb9925c985d87b838f2688a52227c095034d8))

## [1.2.1](https://github.com/ClashStrategic/webapp/compare/v1.2.0...v1.2.1) (2025-06-25)


### Bug Fixes

* **config:** change default sound effects setting to disabled ([c4bc7ff](https://github.com/ClashStrategic/webapp/commit/c4bc7ff72d886de27bdcb64efc92df7580177b0d))
* **config:** prevent overwriting existing sound effects setting ([796165e](https://github.com/ClashStrategic/webapp/commit/796165edd1ac36acf0e3772caf0cf798f52dfb02))

# [1.2.0](https://github.com/ClashStrategic/webapp/compare/v1.1.2...v1.2.0) (2025-06-25)


### Bug Fixes

* **deck:** add batch operation flag to prevent interruptions during deck operations ([48afddd](https://github.com/ClashStrategic/webapp/commit/48afdddeb532bad865f805965bf24f1bc9e8cf0c))


### Features

* Allow specific slot placement for cards in deck ([cf6967e](https://github.com/ClashStrategic/webapp/commit/cf6967eca7c6ac6ddbb96a4d77e7008207aba967))
* **api:** add deck saving state management for update-deck operations ([7f0ca3e](https://github.com/ClashStrategic/webapp/commit/7f0ca3e27d638abbcba3b414067e966650996186))
* **deck:** add batch operation control and auto-save for card replacement ([23d4c2b](https://github.com/ClashStrategic/webapp/commit/23d4c2bceb546721db24e12626e39910f57ecae1))
* **deck:** add retry mechanism for concurrent save operations ([c8013f9](https://github.com/ClashStrategic/webapp/commit/c8013f9c1bbc6c89a4d421143d97aa887ffcb472))
* **deck:** add validation to prevent card replacement in incomplete decks ([196b8ba](https://github.com/ClashStrategic/webapp/commit/196b8ba02c60de81171bd54610aa192f221b5c26))
* **deck:** add validation to prevent replacement of cards already in deck ([7edef9a](https://github.com/ClashStrategic/webapp/commit/7edef9a11a2ac557aed0f7f59556d72678d320ad))
* **deck:** add validation to prevent tower card replacement ([eba6776](https://github.com/ClashStrategic/webapp/commit/eba67760d7c5819c7a73eee2d45ecafed3af5c37))
* **deck:** add visual feedback for card replacement in main deck collection ([56ed238](https://github.com/ClashStrategic/webapp/commit/56ed2382c5783fd552468a549339080ea839f93c))
* **deck:** allows you to save the incomplete deck in the database ([7cc68aa](https://github.com/ClashStrategic/webapp/commit/7cc68aa1d8c07e5ae16e6236e7992f1c371d8152))
* **deck:** trigger analysis after batch operations complete ([b93fe38](https://github.com/ClashStrategic/webapp/commit/b93fe3847e65890cd996a47818ccdb0d9413da5c))

## [1.1.2](https://github.com/ClashStrategic/webapp/compare/v1.1.1...v1.1.2) (2025-06-23)


### Bug Fixes

* Persist user and session data in local storage ([6dc7300](https://github.com/ClashStrategic/webapp/commit/6dc73008edb97ba2236fd067d09fd2a0889598fe))
* **sound_effects:** Initialize sound effects setting in home.html ([cc90f9c](https://github.com/ClashStrategic/webapp/commit/cc90f9cfb73f415657a79c924b56b3b04a5bba4b))

## [1.1.1](https://github.com/ClashStrategic/webapp/compare/v1.1.0...v1.1.1) (2025-06-22)


### Bug Fixes

* **cards:** exclude tower slot from deck data and save changes after move ([d9a647c](https://github.com/ClashStrategic/webapp/commit/d9a647c174d7992697f07a1c2452d1c101ee9c6f))
* **cards:** prevent empty slot clicks during deck updates ([8263e6f](https://github.com/ClashStrategic/webapp/commit/8263e6f2ea037b672938f4906a716590d3808884))
* **Cards:** Prevent moving cards to tower slot in Card.js ([eec2b7e](https://github.com/ClashStrategic/webapp/commit/eec2b7e945af3427a1a0734e0aa56f779a0c0721))
* **cards:** prevent tower cards from being selected for move ([8d43606](https://github.com/ClashStrategic/webapp/commit/8d43606f4b3368df38b604e04a91ff42be5b7922))
* **Cards:** Update deck saving logic to check for completeness ([f377816](https://github.com/ClashStrategic/webapp/commit/f3778166c8482074879014b59190f47d63c2ad4e))
* **deck:** optimize database saves by checking for actual changes ([02f065d](https://github.com/ClashStrategic/webapp/commit/02f065d96168f94e742677c765ff31833d817e96))
* **events:** improve click event handling with proper event parameter passing ([8ca499e](https://github.com/ClashStrategic/webapp/commit/8ca499e52c88dbc72d7c0cee83feab02d5453723))

# [1.1.0](https://github.com/ClashStrategic/webapp/compare/v1.0.1...v1.1.0) (2025-06-20)


### Bug Fixes

* **ui:** correct Spanish text with proper accents and improve consistency ([263429c](https://github.com/ClashStrategic/webapp/commit/263429c9a523b70baf80f5b1139c71e518612aee))


### Features

* **ui:** add collapsible deck analysis section with toggle button ([f8909a0](https://github.com/ClashStrategic/webapp/commit/f8909a0f303937305321dbc5b2741e52baf65671))
* **ui:** add deck statistics display to card section ([e2eed79](https://github.com/ClashStrategic/webapp/commit/e2eed795d6b8e74f8c9d4abfa07758de7c3ac1c4))

## [1.0.1](https://github.com/ClashStrategic/webapp/compare/v1.0.0...v1.0.1) (2025-06-17)


### Bug Fixes

* Use 'invitado' for guest user check in HomeView ([00a2ac7](https://github.com/ClashStrategic/webapp/commit/00a2ac7c9a9b6c57c2234579a735007129de5ec3))

# [1.0.0](https://github.com/ClashStrategic/webapp/compare/v0.6.5...v1.0.0) (2025-06-17)


### Bug Fixes

* **Cards:** Eliminates unnecessary for when setting up cards ([422b39e](https://github.com/ClashStrategic/webapp/commit/422b39e0548f56eb53d0888d2ed47ccc20de1bef))
* **Deck:** Ensure Mazos cookie initializes correctly for guests ([5d81885](https://github.com/ClashStrategic/webapp/commit/5d81885a2ece1a0942fde944e2a80351251dca74))
* **Deck:** Update API call for deck analysis functionality ([4742400](https://github.com/ClashStrategic/webapp/commit/4742400c88956b94e5c09964a110d05b4bce1a5c))
* Integrates the letter's media data into the data-json attribute and improves the log messages for the json values ​​so they can be displayed correctly in the console. ([1860a5b](https://github.com/ClashStrategic/webapp/commit/1860a5b051324d06d883422cd4d91dac43240321))


### Code Refactoring

* **api:** require explicit HTTP method for all API calls ([1d20fea](https://github.com/ClashStrategic/webapp/commit/1d20feab756056c7236373201c17153c1675da93))


### Features

* **api-v1:** Add url parameter and restructure api function signature ([860a4e6](https://github.com/ClashStrategic/webapp/commit/860a4e6ef1486f35f005ee8c85a061a58d93ac9c))
* **cache templates:** Add `src/templates` to directories scanned by URL lister ([5e415a4](https://github.com/ClashStrategic/webapp/commit/5e415a4cc70be03418e7efb4180330d2bc1cd89f))
* **cards:** Render card collection using client-side templates ([b80bf48](https://github.com/ClashStrategic/webapp/commit/b80bf480e8cb881cda5083c9732da8972aa742cf))
* **Config:** add async template rendering method ([5381efc](https://github.com/ClashStrategic/webapp/commit/5381efc2282898ee9f0893b532dfec7d3ef45ad5))
* **DeckAnalysis:** Implement dynamic rendering for analysis view ([9a7fc57](https://github.com/ClashStrategic/webapp/commit/9a7fc57e1a5c8b0a468bec9b80b47d69ff17bf56))
* **Info-Cards:** Implement detailed card statistics view ([31b9d2e](https://github.com/ClashStrategic/webapp/commit/31b9d2eb2af10e23480e4924400b32878e8104c7))
* **main:** Add mainJsFullyLoaded event and log message ([a5cfcb6](https://github.com/ClashStrategic/webapp/commit/a5cfcb67cc963797975ae517b272576f8d239920))
* Render About Us content via client-side template ([90ecfcd](https://github.com/ClashStrategic/webapp/commit/90ecfcdac3f7786a426a60ae1dd84fd1790185cf))
* **shop:** Load shop content via dedicated products API ([922338a](https://github.com/ClashStrategic/webapp/commit/922338a4ee61f042059184d9994e891a9bc3805c))
* **templates:** Add several views that were in API as MVC to render it from webapp. ([d038d52](https://github.com/ClashStrategic/webapp/commit/d038d52292042f5a4044d4e8938f82696b8c74c9))


### BREAKING CHANGES

* **api:** The api utility function now requires the HTTP method (e.g., 'GET', 'POST') as its first argument.
All existing calls to api(...) have been updated to use the new signature, e.g., api('GET', '/endpoint').

This change enhances clarity and maintainability by making the request type explicit, reducing ambiguity, and enabling more robust request handling logic in the future.
* **api-v1:** The api() function signature has changed:
- Now requires mandatory url parameter as first argument
- Type parameter moved to second position (also mandatory)

## [0.6.5](https://github.com/ClashStrategic/webapp/compare/v0.6.4...v0.6.5) (2025-06-02)


### Bug Fixes

* **base_url_api:** Modify API base URL configuration for local and production ([d6a125e](https://github.com/ClashStrategic/webapp/commit/d6a125ef79bc87a9afe041a83048b3d3c29a0626))

## [0.6.4](https://github.com/ClashStrategic/webapp/compare/v0.6.3...v0.6.4) (2025-06-02)


### Bug Fixes

* **home:** correct base URL for API in local storage ([1d09d4f](https://github.com/ClashStrategic/webapp/commit/1d09d4f5f495a6bf8afe9f80df9285c450bf529c))

## [0.6.3](https://github.com/ClashStrategic/webapp/compare/v0.6.2...v0.6.3) (2025-05-29)


### Bug Fixes

* **Deck:** correct gem cost retrieval in deck creation confirmation ([56a37da](https://github.com/ClashStrategic/webapp/commit/56a37da5119664fb488c50547dd5b15afaa499bd))
* **Deck:** update gem cost for advanced analysis ([88f81ec](https://github.com/ClashStrategic/webapp/commit/88f81ec0ed1459bd15b1476d624a2ae0aa38da85))

## [0.6.2](https://github.com/ClashStrategic/webapp/compare/v0.6.1...v0.6.2) (2025-05-27)


### Bug Fixes

* **sw:** include version in update alert message ([1dbd619](https://github.com/ClashStrategic/webapp/commit/1dbd619e86172447948fd44612986cd02217f431))

## [0.6.1](https://github.com/ClashStrategic/webapp/compare/v0.6.0...v0.6.1) (2025-05-27)


### Bug Fixes

* **home:** Integrate the initial HTML with dependencies to avoid errors with scripts ([0d5b304](https://github.com/ClashStrategic/webapp/commit/0d5b30482c7413d9c8fef17b3ccc0d6f3945685a))

# [0.6.0](https://github.com/ClashStrategic/webapp/compare/v0.5.0...v0.6.0) (2025-05-26)


### Features

* **sw:** enhance service worker update handling ([49e2a49](https://github.com/ClashStrategic/webapp/commit/49e2a495c460ded535cfa90ba62be1eb60f618ca))

# [0.5.0](https://github.com/ClashStrategic/webapp/compare/v0.4.3...v0.5.0) (2025-05-26)


### Features

* **sw:** store service worker (webapp) version and build datetime ([0cbc3a1](https://github.com/ClashStrategic/webapp/commit/0cbc3a14831fdf7c8d52adb188ba65ccdf551a93))

## [0.4.3](https://github.com/ClashStrategic/webapp/compare/v0.4.2...v0.4.3) (2025-05-26)


### Bug Fixes

* **api:** remove version check from API response handling ([96bac2c](https://github.com/ClashStrategic/webapp/commit/96bac2cb84d6d596c466cbee7cc00f5c09e07ad3))
* **sw:** update service worker registration and caching logic ([e94c752](https://github.com/ClashStrategic/webapp/commit/e94c7525ef46bbc4dde8c2a70cfed126878bb15b))

## [0.4.2](https://github.com/ClashStrategic/webapp/compare/v0.4.1...v0.4.2) (2025-05-23)


### Bug Fixes

* **tooltip:** Activate fading by pressing outside the tooltip when it is in the general toggle ([0a1a3ce](https://github.com/ClashStrategic/webapp/commit/0a1a3ce2958f6fc1b1fbaf195f18b98ce0750f39))

## [0.4.1](https://github.com/ClashStrategic/webapp/compare/v0.4.0...v0.4.1) (2025-05-23)


### Bug Fixes

* **privacy:** add comprehensive privacy policy document ([52a99b1](https://github.com/ClashStrategic/webapp/commit/52a99b1ed7d8f4c7d5fae9d850f1a24069aa0a49))

# [0.4.0](https://github.com/ClashStrategic/webapp/compare/v0.3.0...v0.4.0) (2025-05-22)


### Bug Fixes

* **home:** store API base URL in localStorage for fetch ([3eb00a7](https://github.com/ClashStrategic/webapp/commit/3eb00a7e571abea0c8d1d4ba3932fb450e3f877c))


### Features

* Configure dynamic API base URL and fetch home content ([3800d67](https://github.com/ClashStrategic/webapp/commit/3800d6716d5364d9d5a1678876da72ae850ba079))

# [0.3.0](https://github.com/ClashStrategic/webapp/compare/v0.2.0...v0.3.0) (2025-05-21)


### Features

* **htaccess, error pages:** add server configuration and error pages ([ca4b0b8](https://github.com/ClashStrategic/webapp/commit/ca4b0b81c2f64672c9397c6f92c56a4353eb7ab7))

# [0.2.0](https://github.com/ClashStrategic/webapp/compare/v0.1.0...v0.2.0) (2025-05-21)


### Features

* add robots.txt for bot access control and sitemap.xml for better indexing ([e1cd5a6](https://github.com/ClashStrategic/webapp/commit/e1cd5a69ffec4481cc18f202b0e11ff54161d0d7))
