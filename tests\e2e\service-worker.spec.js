/**
 * @file End-to-end tests for the Service Worker functionality.
 * This suite verifies the registration, caching, and update mechanisms of the Service Worker.
 * @group Service Worker
 */
import { test, expect } from "@playwright/test";

/**
 * @description Test suite for verifying Service Worker registration and caching.
 */
test.describe("Clash Strategic - Service Worker", () => {
    let consoleMessages;

    test.beforeEach(async ({ page }) => {
        consoleMessages = [];
        page.on("console", (msg) => {
            consoleMessages.push(msg);
        });
    });

    test.afterEach(async () => {
        const errors = consoleMessages.filter((msg) => msg.type() === "error");
        if (errors.length > 0) {
            const errorDetails = errors
                .map((e) => {
                    const location = e.location();
                    return `${e.text()} (at ${location.url}:${location.lineNumber}:${location.columnNumber
                        })`;
                })
                .join("\n- ");
            const errorMessage = `Test failed due to ${errors.length} console errors:\n- ${errorDetails}`;
            expect(errors, errorMessage).toHaveLength(0);
        }
    });

    /**
     * @description Test to verify that the Service Worker registers successfully on key pages.
     */
    const pagesToTest = ["home.html", "index.html"];
    for (const pageName of pagesToTest) {
        test(`should register Service Worker without errors on ${pageName}`, async ({ page }) => {
            // Navigate to the page and wait for the network to be idle
            await page.goto(`/${pageName}`, { waitUntil: "networkidle" });

            // Wait for the Service Worker registration to be initiated and completed
            const swRegistration = await page.evaluate(async () => {
                if ("serviceWorker" in navigator) {
                    try {
                        const registration = await navigator.serviceWorker.ready;
                        return {
                            scope: registration.scope,
                            active: !!registration.active,
                        };
                    } catch (e) {
                        return { error: e.message };
                    }
                }
                return { error: "Service Worker not supported" };
            });

            // Assert that the registration was successful
            expect(swRegistration.error, "Service Worker registration failed").toBeUndefined();
            expect(swRegistration.active, "Service Worker is not active").toBe(true);
            expect(swRegistration.scope).toContain("/");

            // Verify console logs for success messages
            const registrationSuccessLog = consoleMessages.find((msg) =>
                msg.text().includes("SW registrado con éxito")
            );
            expect(
                registrationSuccessLog,
                "Success message 'SW registrado con éxito' not found in console"
            ).toBeDefined();
        });
    }
});

/**
 * @description Test to verify that essential resources are cached and the app is accessible offline.
 */
test("should load from cache when offline", async ({ page, context }) => {
    // Navigate and wait for the SW to be fully active
    await page.goto("/home.html", { waitUntil: "networkidle" });
    await page.evaluate(async () => {
        await navigator.serviceWorker.ready;
    });

    // List of essential resources that should be cached
    const urlsToCache = [
        "/src/js/main.js",
        "/installsw.js",
        "/src/css/main.css",
        "/index.html",
        "/home.html",
        "/error404.html",
    ];

    // Go offline
    await context.setOffline(true);

    // Verify that cached resources are accessible offline
    for (const url of urlsToCache) {
        try {
            const response = await page.goto(url, { waitUntil: "domcontentloaded" });
            expect(
                response.status(),
                `URL ${url} should be accessible offline`
            ).toBe(200);
        } catch (error) {
            // page.goto will throw an error if the navigation fails, which is expected for offline tests.
            // We check the error message to ensure it's a network error, which means it wasn't served from cache.
            expect(
                error.message,
                `URL ${url} failed to load from cache`
            ).not.toContain("net::ERR_INTERNET_DISCONNECTED");
        }
    }

    // Reload the page and check if a key element is visible
    await page.goto("/home.html");
    await page.reload({ waitUntil: "domcontentloaded" });
    await expect(
        page.locator("#capa_contenido"),
        "Main content container should be attached on offline reload"
    ).toBeAttached();

    // Go back online
    await context.setOffline(false);
});

/**
 * @description Test to verify that the Service Worker updates correctly when the version changes.
 */
test("should update Service Worker when version changes", async ({ page }) => {
    // Mock alert to prevent it from blocking the test
    page.on('dialog', dialog => dialog.dismiss());

    // 1. Load the page and wait for the initial SW to be active
    await page.goto("/home.html", { waitUntil: "networkidle" });
    await page.evaluate(async () => {
        await navigator.serviceWorker.ready;
    });

    // Store the initial version from localStorage
    const initialVersion = await page.evaluate(() => localStorage.getItem('sw_version'));
    expect(initialVersion).toBeDefined();

    // 2. Set up the route to serve the new SW version
    const newVersion = "99.9.9";
    await page.route("**/sw.js", (route, request) => {
        // Only intercept if it's an update check (not the initial load)
        if (request.headers()['service-worker'] === 'script') {
            route.fulfill({
                status: 200,
                contentType: "application/javascript",
                body: `
                    const VERSION = '${newVersion}';
                    self.addEventListener('install', () => self.skipWaiting());
                    self.addEventListener('activate', event => {
                        event.waitUntil(
                            self.clients.claim().then(() => {
                                return self.clients.matchAll().then(clients => {
                                    clients.forEach(client => client.postMessage({ type: 'ACTIVATED', version: VERSION }));
                                });
                            })
                        );
                    });
                `,
            });
        } else {
            route.continue();
        }
    });

    // 3. Listen for the 'ACTIVATED' message from the new SW
    const newSWActivated = page.evaluate((expectedVersion) => {
        return new Promise((resolve) => {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'ACTIVATED' && event.data.version === expectedVersion) {
                    resolve(event.data.version);
                }
            });
        });
    }, newVersion);

    // 4. Trigger the SW update check
    await page.evaluate(async () => {
        const reg = await navigator.serviceWorker.getRegistration();
        await reg?.update();
    });

    // 5. Wait for the activation promise and verify the version
    const activatedVersion = await newSWActivated;
    expect(activatedVersion).toBe(newVersion);
});